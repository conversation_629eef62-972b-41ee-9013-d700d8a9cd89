import React, { useState } from 'react';
import { Arrow<PERSON>eft, Loader2, AlertCircle, Check, X } from 'lucide-react';
import { TokenData, CreatedTokenInfo } from './TokenCreator';
import { useCreateToken } from '../hooks/useCreateToken';

interface ReviewAndCreateProps {
  tokenData: TokenData;
  onBack: () => void;
  onTokenCreated: (tokenInfo: CreatedTokenInfo) => void;
}

export const ReviewAndCreate: React.FC<ReviewAndCreateProps> = ({ tokenData, onBack, onTokenCreated }) => {
  const [isCreating, setIsCreating] = useState(false);
  const { createToken, estimatedCost } = useCreateToken();

  const handleCreate = async () => {
    setIsCreating(true);
    try {
      const result = await createToken(tokenData);
      onTokenCreated(result);
    } catch (error) {
      console.error('Token creation failed:', error);
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Review & Create Token</h2>
        <p className="text-gray-600 mb-6">
          Please review your token details before creating. This action cannot be undone.
        </p>

        {/* Token Preview */}
        <div className="bg-gray-50 rounded-lg p-6 mb-6">
          <div className="flex items-start space-x-4">
            {tokenData.imageUrl && (
              <img
                src={tokenData.imageUrl}
                alt={tokenData.name}
                className="w-16 h-16 rounded-lg object-cover flex-shrink-0"
              />
            )}
            <div className="flex-1">
              <h3 className="text-xl font-bold text-gray-900">{tokenData.name}</h3>
              <p className="text-lg text-gray-600 font-medium">${tokenData.symbol}</p>
              <p className="text-sm text-gray-600 mt-2">{tokenData.description}</p>
            </div>
          </div>
        </div>

        {/* Token Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Total Supply</label>
              <p className="text-lg font-semibold text-gray-900">
                {Number(tokenData.totalSupply).toLocaleString()} {tokenData.symbol}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Decimal Places</label>
              <p className="text-lg font-semibold text-gray-900">{tokenData.decimals}</p>
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Authorities</label>
              <div className="flex items-center space-x-2">
                {tokenData.revokeAuthorities ? (
                  <>
                    <X className="h-4 w-4 text-red-500" />
                    <span className="text-sm text-gray-900">Will be revoked after creation</span>
                  </>
                ) : (
                  <>
                    <Check className="h-4 w-4 text-green-500" />
                    <span className="text-sm text-gray-900">Will be retained</span>
                  </>
                )}
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Network</label>
              <p className="text-lg font-semibold text-gray-900">Solana Devnet</p>
            </div>
          </div>
        </div>

        {/* Cost Estimation */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div className="flex items-start space-x-3">
            <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="font-medium text-yellow-800">Estimated Transaction Cost</h4>
              <p className="text-sm text-yellow-700 mt-1">
                Creating this token will cost approximately <strong>{estimatedCost} SOL</strong> in transaction fees.
                This includes token creation, metadata upload, and authority management.
              </p>
            </div>
          </div>
        </div>

        {/* Warnings */}
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-start space-x-3">
            <AlertCircle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="font-medium text-red-800">Important Notes</h4>
              <ul className="text-sm text-red-700 mt-1 space-y-1">
                <li>• Token creation is permanent and cannot be undone</li>
                <li>• Make sure all details are correct before proceeding</li>
                <li>• You will receive all tokens in your connected wallet</li>
                {tokenData.revokeAuthorities && (
                  <li>• Mint and freeze authorities will be permanently revoked</li>
                )}
              </ul>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <button
          type="button"
          onClick={onBack}
          disabled={isCreating}
          className="flex items-center space-x-2 btn-secondary disabled:opacity-50"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back</span>
        </button>
        
        <button
          type="button"
          onClick={handleCreate}
          disabled={isCreating}
          className="flex items-center space-x-2 btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isCreating ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Creating Token...</span>
            </>
          ) : (
            <span>Create Token</span>
          )}
        </button>
      </div>
    </div>
  );
};
