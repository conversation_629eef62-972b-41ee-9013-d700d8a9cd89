import React from 'react';
import { useConnection, useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';
import { LAMPORTS_PER_SOL } from '@solana/web3.js';
import { Coins, Wallet } from 'lucide-react';
import { useWalletBalance } from '../hooks/useWalletBalance';

export const Header: React.FC = () => {
  const { connection } = useConnection();
  const { publicKey, connected } = useWallet();
  const balance = useWalletBalance();

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="bg-gradient-to-r from-solana-purple to-primary-600 p-2 rounded-lg">
              <Coins className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">Solana Token Creator</h1>
              <p className="text-sm text-gray-500">Create SPL tokens with ease</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            {connected && publicKey && (
              <div className="flex items-center space-x-2 bg-gray-50 px-3 py-2 rounded-lg">
                <Wallet className="h-4 w-4 text-gray-600" />
                <div className="text-sm">
                  <div className="font-medium text-gray-900">
                    {balance !== null ? `${balance.toFixed(4)} SOL` : 'Loading...'}
                  </div>
                  <div className="text-gray-500 text-xs">
                    {publicKey.toString().slice(0, 4)}...{publicKey.toString().slice(-4)}
                  </div>
                </div>
              </div>
            )}
            <WalletMultiButton className="!bg-gradient-to-r !from-solana-purple !to-primary-600 hover:!from-solana-purple/90 hover:!to-primary-700" />
          </div>
        </div>
      </div>
    </header>
  );
};
