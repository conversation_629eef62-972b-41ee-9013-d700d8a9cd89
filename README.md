# Solana Token Creator

A comprehensive web application for creating Solana SPL tokens with custom metadata, images, and configurable authorities.

## Features

- 🔗 **Phantom Wallet Integration** - Connect your wallet to use your SOL for transactions
- 🎨 **Custom Token Metadata** - Add name, symbol, description, and supply
- 🖼️ **Image Upload** - Upload token logos/artwork with IPFS storage
- ⚙️ **Authority Management** - Choose to revoke mint and freeze authorities
- 📱 **Responsive Design** - Works on desktop and mobile
- 🔍 **Transaction Tracking** - View your tokens on Solana Explorer
- 💰 **Cost Estimation** - See estimated transaction costs before creation

## Getting Started

### Prerequisites

- Node.js 16+ installed
- Phantom wallet browser extension
- Some SOL in your wallet for transaction fees (Devnet)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd solana-token-creator
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

### Usage

1. **Connect Wallet**: Click "Connect Wallet" and select Phantom
2. **Token Details**: Fill in your token information (name, symbol, description, supply)
3. **Upload Image**: Add a logo or artwork for your token
4. **Review & Create**: Review details and create your token
5. **Success**: View your token on Solana Explorer

## Configuration

### IPFS Storage

The application currently uses a mock IPFS implementation for demo purposes. To use real IPFS storage, update `src/utils/ipfs.ts` with your preferred service:

- **Pinata**: Add your JWT token
- **NFT.Storage**: Add your API key
- **Web3.Storage**: Add your API token

### Network Configuration

The app is configured for Solana Devnet. To change networks, update `src/contexts/WalletContextProvider.tsx`:

```typescript
const network = WalletAdapterNetwork.Mainnet; // or Testnet
```

## Project Structure

```
src/
├── components/          # React components
│   ├── Header.tsx      # App header with wallet connection
│   ├── TokenCreator.tsx # Main token creation wizard
│   ├── TokenForm.tsx   # Token details form
│   ├── ImageUpload.tsx # Image upload component
│   ├── ReviewAndCreate.tsx # Review and creation step
│   └── TokenCreated.tsx # Success page
├── contexts/           # React contexts
│   └── WalletContextProvider.tsx # Wallet connection context
├── hooks/              # Custom React hooks
│   ├── useCreateToken.ts # Token creation logic
│   └── useWalletBalance.ts # Wallet balance hook
├── utils/              # Utility functions
│   └── ipfs.ts        # IPFS upload utilities
├── App.tsx            # Main app component
├── main.tsx           # App entry point
└── index.css          # Global styles
```

## Technologies Used

- **React 18** - UI framework
- **TypeScript** - Type safety
- **Vite** - Build tool
- **Tailwind CSS** - Styling
- **Solana Web3.js** - Solana blockchain interaction
- **Metaplex** - Token metadata standards
- **Solana Wallet Adapter** - Wallet integration

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For issues and questions:
- Create an issue on GitHub
- Check the Solana documentation
- Visit the Metaplex documentation
