import React from 'react';
import { Toaster } from 'react-hot-toast';
import { WalletContextProvider } from './contexts/WalletContextProvider';
import { Header } from './components/Header';
import { TokenCreator } from './components/TokenCreator';

function App() {
  return (
    <WalletContextProvider>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <Header />
        <main className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                Create Your Solana Token
              </h1>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Launch your own SPL token on Solana with custom metadata, images, and configurable authorities. 
                No coding required - just connect your wallet and follow the simple steps.
              </p>
            </div>
            <TokenCreator />
          </div>
        </main>
        <Toaster 
          position="bottom-right"
          toastOptions={{
            duration: 5000,
            style: {
              background: '#363636',
              color: '#fff',
            },
          }}
        />
      </div>
    </WalletContextProvider>
  );
}

export default App;
