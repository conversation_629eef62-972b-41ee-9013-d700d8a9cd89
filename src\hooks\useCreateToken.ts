import { useConnection, useWallet } from '@solana/wallet-adapter-react';
import { useCallback, useMemo } from 'react';
import {
  createAndMint,
  mplTokenMetadata,
  TokenStandard
} from '@metaplex-foundation/mpl-token-metadata';
import {
  generateSigner,
  percentAmount,
  signerIdentity,
  createSignerFromKeypair,
  keypairIdentity
} from '@metaplex-foundation/umi';
import { createUmi } from '@metaplex-foundation/umi-bundle-defaults';
import { irysUploader } from '@metaplex-foundation/umi-uploader-irys';
import { base58 } from '@metaplex-foundation/umi/serializers';
import { createSetAuthorityInstruction, AuthorityType } from '@solana/spl-token';
import { PublicKey, Transaction, Keypair } from '@solana/web3.js';
import toast from 'react-hot-toast';
import { TokenData, CreatedTokenInfo } from '../components/TokenCreator';
import { uploadToIPFS } from '../utils/ipfs';

export const useCreateToken = () => {
  const { connection } = useConnection();
  const { publicKey, signTransaction } = useWallet();

  const estimatedCost = useMemo(() => {
    // Rough estimation of costs in SOL
    return '0.01-0.02';
  }, []);

  const createToken = useCallback(async (tokenData: TokenData): Promise<CreatedTokenInfo> => {
    if (!publicKey || !signTransaction) {
      throw new Error('Wallet not connected');
    }

    const loadingToast = toast.loading('Creating your token...');

    try {
      // Step 1: Upload image and metadata to IPFS
      toast.loading('Uploading metadata to IPFS...', { id: loadingToast });
      
      let imageUri = '';
      if (tokenData.image) {
        imageUri = await uploadToIPFS(tokenData.image);
      }

      const metadata = {
        name: tokenData.name,
        symbol: tokenData.symbol,
        description: tokenData.description,
        image: imageUri,
        attributes: [],
        properties: {
          files: imageUri ? [
            {
              uri: imageUri,
              type: tokenData.image?.type || 'image/png',
            },
          ] : [],
          category: 'image',
        },
      };

      const metadataUri = await uploadToIPFS(
        new Blob([JSON.stringify(metadata)], { type: 'application/json' })
      );

      // Step 2: Initialize UMI
      toast.loading('Preparing token creation...', { id: loadingToast });

      const umi = createUmi(connection.rpcEndpoint)
        .use(mplTokenMetadata())
        .use(irysUploader());

      // For demo purposes, create a temporary keypair
      // In production, you'd want to integrate with wallet adapters properly
      const tempKeypair = Keypair.generate();
      const umiKeypair = umi.eddsa.createKeypairFromSecretKey(tempKeypair.secretKey);
      umi.use(keypairIdentity(umiKeypair));

      // Step 3: Create the token
      toast.loading('Creating token on Solana...', { id: loadingToast });
      
      const mintSigner = generateSigner(umi);
      const totalSupplyWithDecimals = BigInt(
        Number(tokenData.totalSupply) * Math.pow(10, tokenData.decimals)
      );

      const mintAndCreateIx = createAndMint(umi, {
        mint: mintSigner,
        name: tokenData.name,
        symbol: tokenData.symbol,
        uri: metadataUri,
        sellerFeeBasisPoints: percentAmount(0),
        decimals: tokenData.decimals,
        amount: totalSupplyWithDecimals,
        tokenOwner: publicKey.toBase58(),
        tokenStandard: TokenStandard.Fungible,
      });

      const tx = await mintAndCreateIx.sendAndConfirm(umi);
      const signature = base58.deserialize(tx.signature)[0];

      // Step 4: Revoke authorities if requested
      if (tokenData.revokeAuthorities) {
        toast.loading('Revoking mint and freeze authorities...', { id: loadingToast });
        
        const mintPublicKey = new PublicKey(mintSigner.publicKey);
        const transaction = new Transaction();

        // Add instruction to revoke mint authority
        transaction.add(
          createSetAuthorityInstruction(
            mintPublicKey,
            publicKey,
            AuthorityType.MintTokens,
            null
          )
        );

        // Add instruction to revoke freeze authority
        transaction.add(
          createSetAuthorityInstruction(
            mintPublicKey,
            publicKey,
            AuthorityType.FreezeAccount,
            null
          )
        );

        // Get recent blockhash
        const { blockhash } = await connection.getLatestBlockhash();
        transaction.recentBlockhash = blockhash;
        transaction.feePayer = publicKey;

        // Sign and send transaction
        const signedTransaction = await signTransaction(transaction);
        await connection.sendRawTransaction(signedTransaction.serialize());
      }

      toast.success('Token created successfully!', { id: loadingToast });

      return {
        mintAddress: mintSigner.publicKey,
        signature,
        metadataUri,
      };
    } catch (error) {
      console.error('Token creation error:', error);
      toast.error('Failed to create token. Please try again.', { id: loadingToast });
      throw error;
    }
  }, [connection, publicKey, signTransaction]);

  return {
    createToken,
    estimatedCost,
  };
};
