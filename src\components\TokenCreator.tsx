import React, { useState } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';
import { Wallet, ArrowRight, ArrowLeft } from 'lucide-react';
import { TokenForm } from './TokenForm';
import { ImageUpload } from './ImageUpload';
import { ReviewAndCreate } from './ReviewAndCreate';
import { TokenCreated } from './TokenCreated';

export interface TokenData {
  name: string;
  symbol: string;
  description: string;
  totalSupply: string;
  decimals: number;
  image: File | null;
  imageUrl?: string;
  revokeAuthorities: boolean;
}

export interface CreatedTokenInfo {
  mintAddress: string;
  signature: string;
  metadataUri: string;
}

const STEPS = [
  { id: 1, title: 'Token Details', description: 'Basic information about your token' },
  { id: 2, title: 'Upload Image', description: 'Add a logo or artwork for your token' },
  { id: 3, title: 'Review & Create', description: 'Review details and create your token' },
  { id: 4, title: 'Token Created', description: 'Your token has been successfully created' },
];

export const TokenCreator: React.FC = () => {
  const { connected } = useWallet();
  const [currentStep, setCurrentStep] = useState(1);
  const [tokenData, setTokenData] = useState<TokenData>({
    name: '',
    symbol: '',
    description: '',
    totalSupply: '',
    decimals: 9,
    image: null,
    revokeAuthorities: true,
  });
  const [createdToken, setCreatedToken] = useState<CreatedTokenInfo | null>(null);

  const handleNext = () => {
    if (currentStep < STEPS.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleTokenCreated = (tokenInfo: CreatedTokenInfo) => {
    setCreatedToken(tokenInfo);
    setCurrentStep(4);
  };

  if (!connected) {
    return (
      <div className="card text-center">
        <div className="mb-6">
          <Wallet className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Connect Your Wallet</h2>
          <p className="text-gray-600 mb-6">
            Connect your Phantom wallet to start creating your Solana token
          </p>
        </div>
        <WalletMultiButton className="!bg-gradient-to-r !from-solana-purple !to-primary-600 hover:!from-solana-purple/90 hover:!to-primary-700" />
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Progress Steps */}
      <div className="card">
        <div className="flex items-center justify-between">
          {STEPS.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className="flex items-center">
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center font-semibold ${
                    currentStep >= step.id
                      ? 'bg-gradient-to-r from-solana-purple to-primary-600 text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}
                >
                  {step.id}
                </div>
                <div className="ml-3 hidden sm:block">
                  <div className={`font-medium ${currentStep >= step.id ? 'text-gray-900' : 'text-gray-500'}`}>
                    {step.title}
                  </div>
                  <div className="text-sm text-gray-500">{step.description}</div>
                </div>
              </div>
              {index < STEPS.length - 1 && (
                <ArrowRight className="h-5 w-5 text-gray-400 mx-4 hidden sm:block" />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <div className="card">
        {currentStep === 1 && (
          <TokenForm
            tokenData={tokenData}
            setTokenData={setTokenData}
            onNext={handleNext}
          />
        )}
        {currentStep === 2 && (
          <ImageUpload
            tokenData={tokenData}
            setTokenData={setTokenData}
            onNext={handleNext}
            onBack={handleBack}
          />
        )}
        {currentStep === 3 && (
          <ReviewAndCreate
            tokenData={tokenData}
            onBack={handleBack}
            onTokenCreated={handleTokenCreated}
          />
        )}
        {currentStep === 4 && createdToken && (
          <TokenCreated tokenInfo={createdToken} tokenData={tokenData} />
        )}
      </div>
    </div>
  );
};
