import React from 'react';
import { <PERSON><PERSON>ir<PERSON>, ExternalLink, Copy, RefreshCw } from 'lucide-react';
import { TokenData, CreatedTokenInfo } from './TokenCreator';
import toast from 'react-hot-toast';

interface TokenCreatedProps {
  tokenInfo: CreatedTokenInfo;
  tokenData: TokenData;
}

export const TokenCreated: React.FC<TokenCreatedProps> = ({ tokenInfo, tokenData }) => {
  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard!`);
  };

  const handleCreateAnother = () => {
    window.location.reload();
  };

  return (
    <div className="text-center space-y-6">
      <div className="flex justify-center">
        <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
          <CheckCircle className="h-12 w-12 text-green-600" />
        </div>
      </div>

      <div>
        <h2 className="text-3xl font-bold text-gray-900 mb-2">Token Created Successfully!</h2>
        <p className="text-gray-600">
          Your Solana SPL token has been created and is now live on the blockchain.
        </p>
      </div>

      {/* Token Preview */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6">
        <div className="flex items-center justify-center space-x-4 mb-4">
          {tokenData.imageUrl && (
            <img
              src={tokenData.imageUrl}
              alt={tokenData.name}
              className="w-16 h-16 rounded-lg object-cover"
            />
          )}
          <div>
            <h3 className="text-2xl font-bold text-gray-900">{tokenData.name}</h3>
            <p className="text-lg text-gray-600 font-medium">${tokenData.symbol}</p>
          </div>
        </div>
        <p className="text-sm text-gray-600">{tokenData.description}</p>
      </div>

      {/* Token Details */}
      <div className="space-y-4">
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="text-left">
              <label className="text-sm font-medium text-gray-500">Token Mint Address</label>
              <p className="text-sm font-mono text-gray-900 break-all">{tokenInfo.mintAddress}</p>
            </div>
            <button
              onClick={() => copyToClipboard(tokenInfo.mintAddress, 'Mint address')}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <Copy className="h-4 w-4" />
            </button>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="text-left">
              <label className="text-sm font-medium text-gray-500">Transaction Signature</label>
              <p className="text-sm font-mono text-gray-900 break-all">{tokenInfo.signature}</p>
            </div>
            <button
              onClick={() => copyToClipboard(tokenInfo.signature, 'Transaction signature')}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <Copy className="h-4 w-4" />
            </button>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="text-left">
              <label className="text-sm font-medium text-gray-500">Metadata URI</label>
              <p className="text-sm font-mono text-gray-900 break-all">{tokenInfo.metadataUri}</p>
            </div>
            <button
              onClick={() => copyToClipboard(tokenInfo.metadataUri, 'Metadata URI')}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <Copy className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <a
          href={`https://explorer.solana.com/address/${tokenInfo.mintAddress}?cluster=devnet`}
          target="_blank"
          rel="noopener noreferrer"
          className="flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
        >
          <span>View on Solana Explorer</span>
          <ExternalLink className="h-4 w-4" />
        </a>

        <a
          href={`https://explorer.solana.com/tx/${tokenInfo.signature}?cluster=devnet`}
          target="_blank"
          rel="noopener noreferrer"
          className="flex items-center justify-center space-x-2 bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
        >
          <span>View Transaction</span>
          <ExternalLink className="h-4 w-4" />
        </a>

        <button
          onClick={handleCreateAnother}
          className="flex items-center justify-center space-x-2 btn-secondary"
        >
          <RefreshCw className="h-4 w-4" />
          <span>Create Another Token</span>
        </button>
      </div>

      {/* Success Tips */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-left">
        <h4 className="font-medium text-blue-800 mb-2">What's Next?</h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Your tokens are now in your connected wallet</li>
          <li>• Share the mint address with others to add your token to their wallets</li>
          <li>• Consider adding liquidity to a DEX like Raydium or Orca</li>
          <li>• Promote your token on social media and crypto communities</li>
          {tokenData.revokeAuthorities && (
            <li>• Mint and freeze authorities have been permanently revoked</li>
          )}
        </ul>
      </div>
    </div>
  );
};
