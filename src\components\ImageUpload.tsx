import React, { useCallback, useState } from 'react';
import { ArrowRight, ArrowLeft, Upload, X, Image as ImageIcon } from 'lucide-react';
import { TokenData } from './TokenCreator';

interface ImageUploadProps {
  tokenData: TokenData;
  setTokenData: (data: TokenData) => void;
  onNext: () => void;
  onBack: () => void;
}

export const ImageUpload: React.FC<ImageUploadProps> = ({ tokenData, setTokenData, onNext, onBack }) => {
  const [dragActive, setDragActive] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(
    tokenData.imageUrl || null
  );

  const handleFile = useCallback((file: File) => {
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setImagePreview(result);
        setTokenData({ ...tokenData, image: file, imageUrl: result });
      };
      reader.readAsDataURL(file);
    }
  }, [tokenData, setTokenData]);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  }, [handleFile]);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  }, [handleFile]);

  const removeImage = () => {
    setImagePreview(null);
    setTokenData({ ...tokenData, image: null, imageUrl: undefined });
  };

  const handleNext = () => {
    onNext();
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Upload Token Image</h2>
        <p className="text-gray-600 mb-6">
          Add a logo or artwork for your token. This will be displayed in wallets and on explorers.
        </p>

        <div className="space-y-4">
          {!imagePreview ? (
            <div
              className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                dragActive
                  ? 'border-primary-500 bg-primary-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <input
                type="file"
                accept="image/*"
                onChange={handleChange}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
              <div className="space-y-4">
                <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                  <Upload className="h-8 w-8 text-gray-400" />
                </div>
                <div>
                  <p className="text-lg font-medium text-gray-900">
                    Drop your image here, or <span className="text-primary-600">browse</span>
                  </p>
                  <p className="text-sm text-gray-500 mt-1">
                    PNG, JPG, JPEG, GIF up to 10MB
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <div className="relative">
              <div className="border-2 border-gray-200 rounded-lg p-4">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <img
                      src={imagePreview}
                      alt="Token preview"
                      className="w-24 h-24 object-cover rounded-lg"
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {tokenData.image?.name || 'Token Image'}
                        </p>
                        <p className="text-sm text-gray-500">
                          {tokenData.image?.size ? `${(tokenData.image.size / 1024 / 1024).toFixed(2)} MB` : ''}
                        </p>
                      </div>
                      <button
                        type="button"
                        onClick={removeImage}
                        className="text-gray-400 hover:text-red-500 transition-colors"
                      >
                        <X className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <ImageIcon className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-blue-700">
                <p className="font-medium mb-1">Image Guidelines:</p>
                <ul className="space-y-1 text-xs">
                  <li>• Square images (1:1 ratio) work best</li>
                  <li>• Minimum 400x400 pixels recommended</li>
                  <li>• Clear, high-quality images perform better</li>
                  <li>• Avoid copyrighted content</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <button
          type="button"
          onClick={onBack}
          className="flex items-center space-x-2 btn-secondary"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back</span>
        </button>
        
        <button
          type="button"
          onClick={handleNext}
          className="flex items-center space-x-2 btn-primary"
        >
          <span>Next: Review & Create</span>
          <ArrowRight className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
};
