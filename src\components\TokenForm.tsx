import React from 'react';
import { ArrowRight, Info } from 'lucide-react';
import { TokenData } from './TokenCreator';

interface TokenFormProps {
  tokenData: TokenData;
  setTokenData: (data: TokenData) => void;
  onNext: () => void;
}

export const TokenForm: React.FC<TokenFormProps> = ({ tokenData, setTokenData, onNext }) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onNext();
  };

  const handleInputChange = (field: keyof TokenData, value: string | number | boolean) => {
    setTokenData({ ...tokenData, [field]: value });
  };

  const isFormValid = () => {
    return (
      tokenData.name.trim() !== '' &&
      tokenData.symbol.trim() !== '' &&
      tokenData.description.trim() !== '' &&
      tokenData.totalSupply.trim() !== '' &&
      !isNaN(Number(tokenData.totalSupply)) &&
      Number(tokenData.totalSupply) > 0
    );
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Token Details</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
              Token Name *
            </label>
            <input
              type="text"
              id="name"
              className="input-field"
              placeholder="e.g., My Awesome Token"
              value={tokenData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              required
            />
            <p className="text-xs text-gray-500 mt-1">The full name of your token</p>
          </div>

          <div>
            <label htmlFor="symbol" className="block text-sm font-medium text-gray-700 mb-2">
              Token Symbol *
            </label>
            <input
              type="text"
              id="symbol"
              className="input-field"
              placeholder="e.g., MAT"
              value={tokenData.symbol}
              onChange={(e) => handleInputChange('symbol', e.target.value.toUpperCase())}
              maxLength={10}
              required
            />
            <p className="text-xs text-gray-500 mt-1">Short symbol (usually 3-5 characters)</p>
          </div>
        </div>

        <div className="mt-6">
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
            Description *
          </label>
          <textarea
            id="description"
            rows={4}
            className="input-field"
            placeholder="Describe your token, its purpose, and utility..."
            value={tokenData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            required
          />
          <p className="text-xs text-gray-500 mt-1">Detailed description of your token</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          <div>
            <label htmlFor="totalSupply" className="block text-sm font-medium text-gray-700 mb-2">
              Total Supply *
            </label>
            <input
              type="number"
              id="totalSupply"
              className="input-field"
              placeholder="e.g., 1000000"
              value={tokenData.totalSupply}
              onChange={(e) => handleInputChange('totalSupply', e.target.value)}
              min="1"
              required
            />
            <p className="text-xs text-gray-500 mt-1">Total number of tokens to create</p>
          </div>

          <div>
            <label htmlFor="decimals" className="block text-sm font-medium text-gray-700 mb-2">
              Decimal Places
            </label>
            <select
              id="decimals"
              className="input-field"
              value={tokenData.decimals}
              onChange={(e) => handleInputChange('decimals', Number(e.target.value))}
            >
              {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9].map((num) => (
                <option key={num} value={num}>
                  {num} {num === 9 ? '(recommended)' : ''}
                </option>
              ))}
            </select>
            <p className="text-xs text-gray-500 mt-1">Number of decimal places (9 is standard)</p>
          </div>
        </div>

        <div className="mt-6">
          <div className="flex items-start space-x-3">
            <input
              type="checkbox"
              id="revokeAuthorities"
              className="mt-1"
              checked={tokenData.revokeAuthorities}
              onChange={(e) => handleInputChange('revokeAuthorities', e.target.checked)}
            />
            <div>
              <label htmlFor="revokeAuthorities" className="text-sm font-medium text-gray-700">
                Revoke Mint and Freeze Authorities
              </label>
              <div className="flex items-start space-x-2 mt-1">
                <Info className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                <p className="text-xs text-gray-600">
                  Recommended for most tokens. This prevents you from minting more tokens or freezing accounts after creation, 
                  making your token more trustworthy and decentralized.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <button
          type="submit"
          disabled={!isFormValid()}
          className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-semibold transition-all duration-200 ${
            isFormValid()
              ? 'btn-primary'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          <span>Next: Upload Image</span>
          <ArrowRight className="h-4 w-4" />
        </button>
      </div>
    </form>
  );
};
